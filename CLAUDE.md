# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

BSA数据生成器 - 基于Python 3.13和Streamlit的统计过程控制数据生成应用。

## 运行命令

```bash
# 安装依赖
pip install -r requirements.txt

# 启动应用
streamlit run bsa_ui.py
```

## 架构

- `bsa_core.py` - 核心业务逻辑，包含数据引擎、导出客户端、配置管理器
- `bsa_ui.py` - Streamlit界面，主入口函数在文件末尾
- `config.toml` - 配置文件，自动生成和持久化

## 关键组件

- `BSADataEngine` - 数据生成和时间序列处理
- `BSAExportClient` - 文件导出和API请求
- `BSAConfigManager` - TOML配置管理
- `BSAUserInterface` - Streamlit UI组件

配置使用扁平化结构，修改配置逻辑需同时更新`_flatten_config()`和`_structure_config()`方法。